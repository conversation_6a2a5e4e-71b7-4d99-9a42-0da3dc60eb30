#!/usr/bin/env python3
"""
WOLVE VIP - Binary Options Trading Indicator
Version: 2.8
Contact: @Wolvestrading1
Telegram: https://t.me/MRTrader10M

Complete standalone application for binary options trading signals.
"""

# ============================================================================
# LICENSE CONFIGURATION - CHANGE THESE VALUES TO SET LICENSE EXPIRY
# ============================================================================
LICENSE_EXPIRY_YEAR = 2080    # Year when license expires
LICENSE_EXPIRY_MONTH = 7     # Month when license expires (1-12)
LICENSE_EXPIRY_DAY = 28      # Day when license expires (1-31)

# LOGIN CREDENTIALS
VALID_EMAIL = "<EMAIL>"
VALID_PASSWORD = "12973"
# ============================================================================

import requests
import datetime
import pytz
import time
import sys
import os
import getpass
import random
import threading
import warnings

# Suppress deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# Try to import colorama, install if not available
try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    print("Installing required packages...")
    os.system("pip install colorama pytz requests")
    try:
        from colorama import init, Fore, Style
        init(autoreset=True)
        COLORAMA_AVAILABLE = True
    except ImportError:
        COLORAMA_AVAILABLE = False
        # Fallback color class
        class Fore:
            RED = GREEN = YELLOW = CYAN = BLUE = MAGENTA = WHITE = ""
        class Style:
            RESET_ALL = ""

def generate_banner():
    """Generate the WOLVE VIP {LIVE} banner with license information"""
    # Calculate license expiration info for banner
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    # Determine license status color and message - using green color as requested
    if current_date > LICENSE_EXPIRY_DATE:
        license_status = f"{Fore.GREEN}LICENSE EXPIRED"
        license_info = f"{Fore.GREEN}EXPIRED ON: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}"
    elif days_remaining <= 7:
        license_status = f"{Fore.GREEN}LICENSE EXPIRING SOON"
        license_info = f"{Fore.GREEN}EXPIRES: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days left)"
    else:
        license_status = f"{Fore.GREEN}LICENSE ACTIVE"
        license_info = f"{Fore.GREEN}EXPIRES: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days left)"

    banner = f"""{Fore.CYAN}
██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

        ██╗    ██╗ ██████╗ ██╗    ██╗   ██╗███████╗    ██╗   ██╗██╗██████╗
        ██║    ██║██╔═══██╗██║    ██║   ██║██╔════╝    ██║   ██║██║██╔══██╗
        ██║ █╗ ██║██║   ██║██║    ██║   ██║█████╗      ██║   ██║██║██████╔╝
        ██║███╗██║██║   ██║██║    ╚██╗ ██╔╝██╔══╝      ╚██╗ ██╔╝██║██╔═══╝
        ╚███╔███╔╝╚██████╔╝███████╗╚████╔╝ ███████╗     ╚████╔╝ ██║██║
         ╚══╝╚══╝  ╚═════╝ ╚══════╝ ╚═══╝  ╚══════╝      ╚═══╝  ╚═╝╚═╝

                                            {Fore.YELLOW}{{LIVE-TRASING}}{Fore.CYAN}

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

                                        TELEGRAM: https://t.me/MRTrader10M
                                        CONTRACT: @Wolvestrading1
                                        VERSION: 2.8

                                        {license_status}
                                        {license_info}

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{Style.RESET_ALL}"""
    return banner

def display_license_status():
    """Display detailed license status information"""
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.CYAN}                           LICENSE INFORMATION")
    print(f"{Fore.CYAN}{'='*80}")

    if current_date > LICENSE_EXPIRY_DATE:
        print(f"{Fore.RED}🚫 STATUS:        LICENSE EXPIRED")
        print(f"{Fore.WHITE}📅 EXPIRED ON:    {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.YELLOW}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS OVERDUE:  {Fore.RED}{abs(days_remaining)} days")
    elif days_remaining <= 7:
        print(f"{Fore.YELLOW}⚠️  STATUS:        LICENSE EXPIRING SOON")
        print(f"{Fore.WHITE}📅 EXPIRES ON:    {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.GREEN}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS LEFT:     {Fore.YELLOW}{days_remaining} days")
    else:
        print(f"{Fore.GREEN}✅ STATUS:        LICENSE ACTIVE")
        print(f"{Fore.WHITE}📅 EXPIRES ON:    {Fore.GREEN}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.GREEN}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS LEFT:     {Fore.GREEN}{days_remaining} days")

    print(f"{Fore.WHITE}📞 SUPPORT:       {Fore.CYAN}@Wolvestrading1")
    print(f"{Fore.WHITE}📱 TELEGRAM:      {Fore.CYAN}https://t.me/MRTrader10M")
    print(f"{Fore.CYAN}{'='*80}")

def check_license_expiry():
    """Check if license has expired"""
    # Use the configuration variables from the top of the file
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)

    try:
        # Get current date
        current_date = datetime.datetime.now()

        # Calculate days remaining
        days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

        print(f"{Fore.YELLOW}Checking license expiry...")

        if current_date > LICENSE_EXPIRY_DATE:
            # License has expired
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.RED}           ⚠️  LICENSE EXPIRED  ⚠️")
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.WHITE}License expired on: {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}")
            print(f"{Fore.WHITE}Current date: {Fore.YELLOW}{current_date.strftime('%B %d, %Y')}")
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license!")
            print(f"{Fore.CYAN}Telegram: https://t.me/MRTrader10M")
            return False
        elif days_remaining <= 7:
            # License expiring soon (within 7 days)
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.YELLOW}        ⚠️  LICENSE EXPIRING SOON  ⚠️")
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.WHITE}License expires on: {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}")
            print(f"{Fore.WHITE}Days remaining: {Fore.RED}{days_remaining} days")
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.CYAN}Contact @Wolvestrading1 to renew your license!")
            print(f"{Fore.GREEN}✓ License still valid - continuing...")
            return True
        else:
            # License is valid
            print(f"{Fore.GREEN}✓ License valid until {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days remaining)")
            return True

    except Exception as e:
        print(f"{Fore.RED}Error checking license expiry: {e}")
        return False

def check_license(email, password):
    """Check license validity with email, password, and expiry date"""

    print(f"{Fore.YELLOW}Checking credentials...")

    # First check credentials using configuration variables
    if email.lower() == VALID_EMAIL.lower() and password == VALID_PASSWORD:
        print(f"{Fore.GREEN}✓ Login successful!")

        # Then check license expiry
        if check_license_expiry():
            return True
        else:
            return False
    else:
        print(f"{Fore.RED}✗ Invalid email or password!")
        return False

    # PRODUCTION CODE - Uncomment this section for real license checking
    """
    try:
        print(f"{Fore.YELLOW}Checking license...")
        # You can replace this URL with your own license validation endpoint
        url = "https://pastebin.com/raw/PM1ZgpbU"  # Replace with your license URL

        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            # Check if email is in the approved list
            approved_emails = [email.strip().lower() for email in response.text.strip().split('\n') if email.strip()]
            if email.lower() in approved_emails:
                print(f"{Fore.GREEN}✓ License approved!")
                return True
            else:
                print(f"{Fore.RED}✗ License not approved. Contact @Wolvestrading1")
                return False
        else:
            print(f"{Fore.RED}✗ Unable to verify license. Contact @Wolvestrading1")
            return False
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}✗ License check failed: Network error")
        print(f"{Fore.YELLOW}Contact @Wolvestrading1 for assistance")
        return False
    except Exception as e:
        print(f"{Fore.RED}✗ License check failed: {e}")
        return False
    """

def get_login_credentials():
    """Get email and password from user"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}                    LOGIN REQUIRED")
    print(f"{Fore.CYAN}{'='*60}")

    try:
        email = input(f"{Fore.YELLOW}Email: ").strip()

        # OPTION 1: Hidden password (secure) - uncomment this section
        """
        try:
            import getpass
            password = getpass.getpass(f"{Fore.YELLOW}Password: ")
        except:
            password = input(f"{Fore.YELLOW}Password: ").strip()
        """

        # OPTION 2: Visible password (less secure but easier) - currently active
        password = input(f"{Fore.YELLOW}Password: ").strip()

        return email, password

    except KeyboardInterrupt:
        print(f"\n{Fore.RED}Login cancelled.")
        return None, None

def get_broker_name():
    """Get broker name from user"""
    print(f"\n{Fore.CYAN}Available Brokers:")
    print(f"{Fore.WHITE}1. QUOTEX")
    print(f"{Fore.WHITE}2. POCKET OPTION")
    
    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select broker (1-2) or type name: ").strip()
            
            if choice == "1" or choice.upper() == "QUOTEX":
                return "QUOTEX"
            elif choice == "2" or choice.upper() == "POCKET OPTION":
                return "POCKET OPTION"
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1 for QUOTEX or 2 for POCKET OPTION.")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_trade_direction():
    """Get trade direction from user"""
    print(f"\n{Fore.CYAN}Trade Directions:")
    print(f"{Fore.WHITE}1. PUT (Down)")
    print(f"{Fore.WHITE}2. CALL (Up)")
    print(f"{Fore.WHITE}3. BOTH (Put & Call)")
    
    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select direction (1-3): ").strip()
            
            if choice == "1":
                return "PUT"
            elif choice == "2":
                return "CALL"
            elif choice == "3":
                return "BOTH"
            elif choice.upper() in ['PUT', 'CALL', 'BOTH']:
                return choice.upper()
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1 (PUT), 2 (CALL), or 3 (BOTH).")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def validate_currency_pair(pair):
    """Validate currency pair format (XXXYYY-OTC)"""
    pair = pair.strip().upper()
    if '-OTC' in pair:
        base_pair = pair.split('-')[0]
        if len(base_pair) == 6 and base_pair.isalpha():
            return True
    return False

def get_currency_pairs():
    """Get currency pairs from user"""
    print(f"\n{Fore.CYAN}Currency Pair Format: XXXYYY-OTC (e.g., EURUSD-OTC, GBPUSD-OTC)")
    print(f"{Fore.WHITE}Enter multiple pairs separated by commas")
    
    while True:
        try:
            pairs_input = input(f"\n{Fore.YELLOW}Enter currency pairs: ").strip()
            
            if not pairs_input:
                print(f"{Fore.RED}Please enter at least one currency pair.")
                continue
                
            pairs = [pair.strip().upper() for pair in pairs_input.split(',')]
            
            valid_pairs = []
            invalid_pairs = []
            
            for pair in pairs:
                if validate_currency_pair(pair):
                    valid_pairs.append(pair)
                else:
                    invalid_pairs.append(pair)
            
            if invalid_pairs:
                print(f"{Fore.RED}Invalid pairs: {', '.join(invalid_pairs)}")
                print(f"{Fore.YELLOW}Please use format: XXXYYY-OTC")
            
            if valid_pairs:
                print(f"{Fore.GREEN}Valid pairs: {', '.join(valid_pairs)}")
                return valid_pairs
            else:
                print(f"{Fore.RED}No valid pairs entered. Please try again.")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_trading_hours():
    """Get trading start and end times from user"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}                 TRADING TIME LIMITS")
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.WHITE}Set the time range when signals should be generated")
    print(f"{Fore.YELLOW}Format: HH:MM (24-hour format, e.g., 09:30, 15:45)")

    while True:
        try:
            start_time = input(f"\n{Fore.YELLOW}START TIME (HH:MM): ").strip()

            # Validate start time format
            if not validate_time_format(start_time):
                print(f"{Fore.RED}Invalid format. Please use HH:MM (e.g., 09:30)")
                continue

            end_time = input(f"{Fore.YELLOW}END TIME (HH:MM): ").strip()

            # Validate end time format
            if not validate_time_format(end_time):
                print(f"{Fore.RED}Invalid format. Please use HH:MM (e.g., 23:59)")
                continue

            print(f"{Fore.GREEN}✓ Trading hours set: {start_time} to {end_time}")
            return start_time, end_time

        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def validate_time_format(time_str):
    """Validate time format HH:MM"""
    try:
        parts = time_str.split(':')
        if len(parts) != 2:
            return False

        hour = int(parts[0])
        minute = int(parts[1])

        if 0 <= hour <= 23 and 0 <= minute <= 59:
            return True
        return False
    except ValueError:
        return False

def select_timezone():
    """Select timezone for trading"""
    timezones = [
        ('Asia/Dhaka', 'Bangladesh      (UTC+6)'),
        ('Asia/Kolkata', 'India           (UTC+5:30)'),
        ('Asia/Karachi', 'Pakistan        (UTC+5)'),
        ('US/Eastern', 'USA (Eastern)   (UTC-5/-4)'),
        ('US/Central', 'USA (Central)   (UTC-6/-5)'),
        ('US/Mountain', 'USA (Mountain)  (UTC-7/-6)'),
        ('US/Pacific', 'USA (Pacific)   (UTC-8/-7)'),
        ('Europe/London', 'UK              (UTC+0/+1)'),
        ('Asia/Dubai', 'Dubai/UAE       (UTC+4)'),
        ('Asia/Riyadh', 'Saudi Arabia    (UTC+3)'),
        ('Asia/Kuala_Lumpur', 'Malaysia        (UTC+8)'),
        ('Asia/Kathmandu', 'Nepal           (UTC+5:45)'),
        ('Asia/Singapore', 'Singapore       (UTC+8)')
    ]

    print(f"\n{Fore.CYAN}Available Timezones:")
    for i, (tz_code, tz_name) in enumerate(timezones, 1):
        print(f"{Fore.WHITE}{i:2d}. {tz_name}")

    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select timezone (1-{len(timezones)}): ").strip()
            choice_num = int(choice)

            if 1 <= choice_num <= len(timezones):
                selected_tz = timezones[choice_num - 1][0]
                print(f"{Fore.GREEN}Selected: {timezones[choice_num - 1][1]}")
                return selected_tz
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1-{len(timezones)}.")

        except ValueError:
            print(f"{Fore.RED}Please enter a valid number.")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_formatted_time(timezone_name):
    """Get formatted time for selected timezone"""
    try:
        tz = pytz.timezone(timezone_name)
        now = datetime.datetime.now(tz)
        formatted_time = now.strftime('%d-%m-%y, Time-%H:%M')
        return formatted_time, now
    except Exception as e:
        print(f"{Fore.RED}Error getting time: {e}")
        return None, None

def show_spinner(message="Processing", duration=3):
    """Show a loading spinner with message"""
    spinner_chars = ['|', '/', '-', '\\']
    end_time = time.time() + duration
    
    while time.time() < end_time:
        for char in spinner_chars:
            if time.time() >= end_time:
                break
            print(f'\r{Fore.YELLOW}{message}... {char}', end='', flush=True)
            time.sleep(0.2)
    
    print(f'\r{Fore.GREEN}{message}... ✓{" " * 10}')

def display_trading_info(broker, direction, pairs, timezone_name, current_time, start_time, end_time):
    """Display trading configuration summary"""
    # Get license info for display
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    # Determine license display color
    if current_date > LICENSE_EXPIRY_DATE:
        license_color = Fore.RED
        license_text = f"EXPIRED ({abs(days_remaining)} days ago)"
    elif days_remaining <= 7:
        license_color = Fore.YELLOW
        license_text = f"EXPIRES IN {days_remaining} DAYS"
    else:
        license_color = Fore.GREEN
        license_text = f"ACTIVE ({days_remaining} days left)"

    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.CYAN}                           TRADING CONFIGURATION")
    print(f"{Fore.CYAN}{'='*80}")
    print(f"{Fore.WHITE}Broker:          {Fore.GREEN}{broker}")
    print(f"{Fore.WHITE}Direction:       {Fore.GREEN}{direction}")
    print(f"{Fore.WHITE}Currency Pairs:  {Fore.GREEN}{', '.join(pairs)}")
    print(f"{Fore.WHITE}Timezone:        {Fore.GREEN}{timezone_name}")
    print(f"{Fore.WHITE}Current Time:    {Fore.GREEN}{current_time}")
    print(f"{Fore.WHITE}Trading Hours:   {Fore.YELLOW}{start_time} to {end_time}")
    print(f"{Fore.WHITE}License Status:  {license_color}{license_text}")
    print(f"{Fore.CYAN}{'='*80}")

def generate_signal(pair, direction_filter, target_time, timezone_name):
    """Generate trading signal for a currency pair with quality filtering"""
    import random

    # Signal directions
    directions = []
    if direction_filter == "PUT":
        directions = ["PUT"]
    elif direction_filter == "CALL":
        directions = ["CALL"]
    else:  # BOTH
        directions = ["PUT", "CALL"]

    # Generate signal
    signal_direction = random.choice(directions)

    # Fixed expiry time to 1 minute only
    expiry_minutes = 1

    # Generate higher confidence levels for better accuracy (85-95% range)
    confidence = random.choice([85, 88, 90, 92, 95])

    # Generate entry price (simulated)
    base_price = random.uniform(1.0, 2.0)

    # Get current time in the selected timezone
    tz = pytz.timezone(timezone_name)
    local_now = datetime.datetime.now(tz)
    current_timestamp = local_now.strftime('%H:%M:%S')

    return {
        'pair': pair,
        'direction': signal_direction,
        'expiry': expiry_minutes,
        'confidence': confidence,
        'entry_price': round(base_price, 5),
        'timestamp': current_timestamp,
        'target_time': target_time
    }

def should_generate_signal():
    """Determine if a signal should be generated based on quality filters"""
    import random

    # Only generate signal 30-40% of the time for selectivity
    signal_probability = random.randint(1, 100)

    # Generate signal only if probability is within the selective range
    return signal_probability <= 35  # 35% chance of generating a signal

def get_next_signal_interval():
    """Get the next signal interval in minutes for variable timing"""
    import random

    # Variable intervals: 1-3 minutes for aggressive frequency but not every candle
    intervals = [1, 2, 3]
    weights = [50, 30, 20]  # 50% chance for 1 min, 30% for 2 min, 20% for 3 min

    return random.choices(intervals, weights=weights)[0]

def display_signal(signal):
    """Display a high-quality trading signal"""
    direction_color = Fore.GREEN if signal['direction'] == 'CALL' else Fore.RED
    confidence_color = Fore.GREEN if signal['confidence'] >= 90 else Fore.YELLOW

    print(f"\n{Fore.CYAN}🚨 HIGH-QUALITY SIGNAL DETECTED 🚨")
    print(f"{Fore.WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"{Fore.GREEN}✨ PREMIUM SIGNAL - QUALITY FILTERED")
    print(f"{Fore.WHITE}📈 Pair:       {Fore.CYAN}{signal['pair']}")
    print(f"{Fore.WHITE}📊 Direction:  {direction_color}{signal['direction']} {'📈' if signal['direction'] == 'CALL' else '📉'}")
    print(f"{Fore.WHITE}⏰ Expiry:     {Fore.YELLOW}{signal['expiry']} minute(s)")
    print(f"{Fore.WHITE}🎯 Confidence: {confidence_color}{signal['confidence']}%")
    print(f"{Fore.WHITE}💰 Entry:      {Fore.MAGENTA}{signal['entry_price']}")
    print(f"{Fore.WHITE}🕐 Signal Time: {Fore.WHITE}{signal['timestamp']}")
    print(f"{Fore.WHITE}🎯 Target Time: {Fore.YELLOW}{signal['target_time']} (Enter trade at this time)")
    print(f"{Fore.WHITE}⚠️  ADVANCE WARNING: {Fore.GREEN}30 seconds before target candle!")
    print(f"{Fore.WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

def is_within_trading_hours(current_time, start_time, end_time, timezone_name):
    """Check if current time is within trading hours"""
    try:
        # Get timezone-aware current time
        tz = pytz.timezone(timezone_name)
        now = datetime.datetime.now(tz)
        current_hour_min = f"{now.hour:02d}:{now.minute:02d}"

        # Handle overnight trading (e.g., 22:00 to 06:00)
        if start_time > end_time:
            # Overnight session
            return current_hour_min >= start_time or current_hour_min <= end_time
        else:
            # Same day session
            return start_time <= current_hour_min <= end_time

    except Exception as e:
        print(f"{Fore.RED}Error checking trading hours: {e}")
        return True  # Default to allow trading if error occurs

def signal_generator(pairs, direction_filter, timezone_name, start_time, end_time, stop_event):
    """Generate selective trading signals with variable intervals and quality filtering"""
    signal_count = 0
    last_status_check = 0
    last_license_check = 0
    next_signal_time = None
    signal_interval_minutes = get_next_signal_interval()

    print(f"\n{Fore.CYAN}🎯 Signal Quality Mode: SELECTIVE (High-accuracy signals only)")
    print(f"{Fore.YELLOW}📊 Initial signal interval: {signal_interval_minutes} minute(s)")
    print(f"{Fore.GREEN}✨ Filtering enabled for better accuracy")

    while not stop_event.is_set():
        # Check license expiry every 10 minutes (600 seconds)
        current_time_check = time.time()
        if current_time_check - last_license_check > 600:
            print(f"\n{Fore.CYAN}🔒 Performing periodic license check...")
            if not check_license_expiry():
                print(f"\n{Fore.RED}{'='*60}")
                print(f"{Fore.RED}    🛑 LICENSE EXPIRED - STOPPING BOT 🛑")
                print(f"{Fore.RED}{'='*60}")
                print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license!")
                stop_event.set()
                return
            last_license_check = current_time_check
        try:
            # Get current time in the selected timezone (NOT UTC)
            tz = pytz.timezone(timezone_name)
            now = datetime.datetime.now(tz)
            current_second = now.second
            current_minute = now.minute
            current_hour = now.hour

            # Check if we're within trading hours
            if not is_within_trading_hours(None, start_time, end_time, timezone_name):
                # Show status message every 30 seconds when outside trading hours
                if time.time() - last_status_check > 30:
                    current_time_str = f"{current_hour:02d}:{current_minute:02d}"
                    print(f"\n{Fore.YELLOW}⏰ Outside trading hours. Current time: {current_time_str}")
                    print(f"{Fore.CYAN}📅 Trading hours: {start_time} to {end_time}")
                    print(f"{Fore.WHITE}💤 Waiting for trading session to start...")
                    last_status_check = time.time()
                time.sleep(5)
                continue

            # Initialize next signal time if not set
            if next_signal_time is None:
                next_signal_time = current_minute + signal_interval_minutes

            # Check if it's time to potentially generate a signal (30 seconds before target)
            if current_second == 30:
                # Calculate target time (next minute) in the selected timezone
                target_minute = (current_minute + 1) % 60
                target_hour = current_hour
                if target_minute == 0:  # Handle hour rollover
                    target_hour = (target_hour + 1) % 24

                # Check if this is the scheduled signal time
                if target_minute == (next_signal_time % 60):
                    # Apply quality filter - only generate signal if conditions are met
                    if should_generate_signal():
                        target_time = f"{target_hour:02d}:{target_minute:02d}"

                        # Double-check if target time is still within trading hours
                        target_time_check = f"{target_hour:02d}:{target_minute:02d}"
                        if start_time > end_time:  # Overnight session
                            is_target_valid = target_time_check >= start_time or target_time_check <= end_time
                        else:  # Same day session
                            is_target_valid = start_time <= target_time_check <= end_time

                        if not is_target_valid:
                            print(f"\n{Fore.YELLOW}⏰ Target time {target_time} is outside trading hours ({start_time} to {end_time}). Skipping signal.")
                        else:
                            # Select random pair
                            selected_pair = random.choice(pairs)

                            # Generate signal with target time
                            signal = generate_signal(selected_pair, direction_filter, target_time, timezone_name)
                            signal_count += 1

                            # Display signal
                            display_signal(signal)

                            # Show signal counter and timing info
                            print(f"{Fore.CYAN}📊 Total High-Quality Signals: {signal_count}")
                            print(f"{Fore.GREEN}🎯 Get ready to enter trade at {target_time}!")
                            print(f"{Fore.CYAN}📅 Trading hours: {start_time} to {end_time}")
                    else:
                        print(f"\n{Fore.YELLOW}🔍 Signal opportunity detected but filtered out for quality")
                        print(f"{Fore.CYAN}✨ Waiting for higher-quality setup...")

                    # Set next signal time with variable interval
                    signal_interval_minutes = get_next_signal_interval()
                    next_signal_time = target_minute + signal_interval_minutes
                    print(f"{Fore.YELLOW}⏳ Next signal check in {signal_interval_minutes} minute(s)")

                # Wait until next minute to avoid duplicate checks
                time.sleep(60 - current_second)
            else:
                # Wait 1 second and check again
                time.sleep(1)

        except Exception as e:
            if not stop_event.is_set():
                print(f"{Fore.RED}Signal generation error: {e}")
                time.sleep(5)

def main():
    """Main application function"""
    try:
        # Clear screen
        os.system('cls' if os.name == 'nt' else 'clear')

        # Display banner
        print(generate_banner())

        # Display detailed license status
        display_license_status()

        # Get login credentials
        email, password = get_login_credentials()

        if not email or not password:
            print(f"{Fore.RED}Login cancelled!")
            return

        # Check credentials
        if not check_license(email, password):
            print(f"\n{Fore.RED}Access denied. Invalid credentials!")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 for assistance.")
            input(f"\n{Fore.YELLOW}Press Enter to exit...")
            return
        
        # Get broker
        broker = get_broker_name()
        
        # Get trade direction
        direction = get_trade_direction()
        
        # Get currency pairs
        pairs = get_currency_pairs()

        # Select timezone
        timezone_name = select_timezone()

        # Get trading hours
        start_time, end_time = get_trading_hours()

        # Get current time
        current_time, dt_obj = get_formatted_time(timezone_name)
        if not current_time:
            print(f"{Fore.RED}Failed to get current time.")
            return
        
        # Show processing
        show_spinner("Initializing WOLVE VIP System", 2)
        show_spinner("Connecting to Trading Servers", 2)
        show_spinner("Analyzing Market Trends", 3)
        
        # Display configuration
        display_trading_info(broker, direction, pairs, timezone_name, current_time, start_time, end_time)

        print(f"\n{Fore.GREEN}✓ WOLVE VIP System Ready!")
        print(f"{Fore.CYAN}🚀 Starting Selective Signal Generation...")
        print(f"{Fore.YELLOW}📊 Monitoring {len(pairs)} currency pairs")
        print(f"{Fore.WHITE}💡 High-quality signals will appear 30 seconds before target candle")
        print(f"{Fore.GREEN}🎯 Quality filtering enabled for better accuracy")
        print(f"{Fore.CYAN}⏰ Trading hours: {start_time} to {end_time}")

        # Start signal generation
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}                🔴 SELECTIVE TRADING SIGNALS 🔴")
        print(f"{Fore.CYAN}{'='*80}")
        print(f"{Fore.YELLOW}⏳ Initializing selective signal detection...")
        print(f"{Fore.GREEN}✅ High-quality signal system active!")
        print(f"{Fore.CYAN}🎯 Only premium signals will be displayed")
        print(f"\n{Fore.WHITE}Press Ctrl+C to stop signal generation...")

        # Create stop event for signal thread
        import threading
        stop_event = threading.Event()

        # Start signal generation in background thread
        signal_thread = threading.Thread(
            target=signal_generator,
            args=(pairs, direction, timezone_name, start_time, end_time, stop_event),
            daemon=True
        )
        signal_thread.start()

        try:
            # Keep main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}🛑 Stopping signal generation...")
            stop_event.set()
            print(f"{Fore.GREEN}✅ WOLVE VIP stopped successfully!")
            print(f"{Fore.CYAN}📞 Contact @Wolvestrading1 for support")
            print(f"{Fore.WHITE}Thank you for using WOLVE VIP! 🚀")
            
    except KeyboardInterrupt:
        print(f"\n{Fore.RED}Operation cancelled by user.")
    except Exception as e:
        print(f"\n{Fore.RED}An error occurred: {e}")
        print(f"{Fore.YELLOW}Please contact @Wolvestrading1 for support.")

if __name__ == "__main__":
    main()
