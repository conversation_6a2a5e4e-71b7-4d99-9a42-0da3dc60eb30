#!/usr/bin/env python3
"""
WOLVE VIP - Binary Options Trading Indicator
Version: 2.8
Contact: @Wolvestrading1
Telegram: https://t.me/MRTrader10M

Complete standalone application for binary options trading signals.
"""

# ============================================================================
# LICENSE CONFIGURATION - CHANGE THESE VALUES TO SET LICENSE EXPIRY
# ============================================================================
LICENSE_EXPIRY_YEAR = 2080    # Year when license expires
LICENSE_EXPIRY_MONTH = 7     # Month when license expires (1-12)
LICENSE_EXPIRY_DAY = 28      # Day when license expires (1-31)

# LOGIN CREDENTIALS
VALID_EMAIL = "<EMAIL>"
VALID_PASSWORD = "12973"
# ============================================================================

import requests
import datetime
import pytz
import time
import sys
import os
import getpass
import random
import threading
import warnings
import numpy as np
import pandas as pd

# Suppress deprecation warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# Try to import required packages, install if not available
try:
    from colorama import init, Fore, Style
    import yfinance as yf
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
    YFINANCE_AVAILABLE = True
except ImportError:
    print("Installing required packages...")
    os.system("pip install colorama pytz requests yfinance numpy pandas")
    try:
        from colorama import init, Fore, Style
        import yfinance as yf
        init(autoreset=True)
        COLORAMA_AVAILABLE = True
        YFINANCE_AVAILABLE = True
    except ImportError:
        COLORAMA_AVAILABLE = False
        YFINANCE_AVAILABLE = False
        # Fallback color class
        class Fore:
            RED = GREEN = YELLOW = CYAN = BLUE = MAGENTA = WHITE = ""
        class Style:
            RESET_ALL = ""

def generate_banner():
    """Generate the WOLVE VIP {LIVE} banner with license information"""
    # Calculate license expiration info for banner
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    # Determine license status color and message - using green color as requested
    if current_date > LICENSE_EXPIRY_DATE:
        license_status = f"{Fore.GREEN}LICENSE EXPIRED"
        license_info = f"{Fore.GREEN}EXPIRED ON: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}"
    elif days_remaining <= 7:
        license_status = f"{Fore.GREEN}LICENSE EXPIRING SOON"
        license_info = f"{Fore.GREEN}EXPIRES: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days left)"
    else:
        license_status = f"{Fore.GREEN}LICENSE ACTIVE"
        license_info = f"{Fore.GREEN}EXPIRES: {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days left)"

    banner = f"""{Fore.CYAN}
██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

        ██╗    ██╗ ██████╗ ██╗    ██╗   ██╗███████╗    ██╗   ██╗██╗██████╗
        ██║    ██║██╔═══██╗██║    ██║   ██║██╔════╝    ██║   ██║██║██╔══██╗
        ██║ █╗ ██║██║   ██║██║    ██║   ██║█████╗      ██║   ██║██║██████╔╝
        ██║███╗██║██║   ██║██║    ╚██╗ ██╔╝██╔══╝      ╚██╗ ██╔╝██║██╔═══╝
        ╚███╔███╔╝╚██████╔╝███████╗╚████╔╝ ███████╗     ╚████╔╝ ██║██║
         ╚══╝╚══╝  ╚═════╝ ╚══════╝ ╚═══╝  ╚══════╝      ╚═══╝  ╚═╝╚═╝        {OTC}

                                            {Fore.YELLOW}{{LIVE-TRADING}}{Fore.CYAN}

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

                                        TELEGRAM: https://t.me/MRTrader10M
                                        CONTRACT: @Wolvestrading1
                                        VERSION: 2.8

                                        {license_status}
                                        {license_info}

██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████
{Style.RESET_ALL}"""
    return banner

def display_license_status():
    """Display detailed license status information"""
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.CYAN}                           LICENSE INFORMATION")
    print(f"{Fore.CYAN}{'='*80}")

    if current_date > LICENSE_EXPIRY_DATE:
        print(f"{Fore.RED}🚫 STATUS:        LICENSE EXPIRED")
        print(f"{Fore.WHITE}📅 EXPIRED ON:    {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.YELLOW}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS OVERDUE:  {Fore.RED}{abs(days_remaining)} days")
    elif days_remaining <= 7:
        print(f"{Fore.YELLOW}⚠️  STATUS:        LICENSE EXPIRING SOON")
        print(f"{Fore.WHITE}📅 EXPIRES ON:    {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.GREEN}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS LEFT:     {Fore.YELLOW}{days_remaining} days")
    else:
        print(f"{Fore.GREEN}✅ STATUS:        LICENSE ACTIVE")
        print(f"{Fore.WHITE}📅 EXPIRES ON:    {Fore.GREEN}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}📅 CURRENT DATE:  {Fore.GREEN}{current_date.strftime('%B %d, %Y at %H:%M')}")
        print(f"{Fore.WHITE}⏰ DAYS LEFT:     {Fore.GREEN}{days_remaining} days")

    print(f"{Fore.WHITE}📞 SUPPORT:       {Fore.CYAN}@Wolvestrading1")
    print(f"{Fore.WHITE}📱 TELEGRAM:      {Fore.CYAN}https://t.me/MRTrader10M")
    print(f"{Fore.CYAN}{'='*80}")

def check_license_expiry():
    """Check if license has expired"""
    # Use the configuration variables from the top of the file
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)

    try:
        # Get current date
        current_date = datetime.datetime.now()

        # Calculate days remaining
        days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

        print(f"{Fore.YELLOW}Checking license expiry...")

        if current_date > LICENSE_EXPIRY_DATE:
            # License has expired
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.RED}           ⚠️  LICENSE EXPIRED  ⚠️")
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.WHITE}License expired on: {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}")
            print(f"{Fore.WHITE}Current date: {Fore.YELLOW}{current_date.strftime('%B %d, %Y')}")
            print(f"{Fore.RED}{'='*60}")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license!")
            print(f"{Fore.CYAN}Telegram: https://t.me/MRTrader10M")
            return False
        elif days_remaining <= 7:
            # License expiring soon (within 7 days)
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.YELLOW}        ⚠️  LICENSE EXPIRING SOON  ⚠️")
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.WHITE}License expires on: {Fore.RED}{LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')}")
            print(f"{Fore.WHITE}Days remaining: {Fore.RED}{days_remaining} days")
            print(f"{Fore.YELLOW}{'='*60}")
            print(f"{Fore.CYAN}Contact @Wolvestrading1 to renew your license!")
            print(f"{Fore.GREEN}✓ License still valid - continuing...")
            return True
        else:
            # License is valid
            print(f"{Fore.GREEN}✓ License valid until {LICENSE_EXPIRY_DATE.strftime('%B %d, %Y')} ({days_remaining} days remaining)")
            return True

    except Exception as e:
        print(f"{Fore.RED}Error checking license expiry: {e}")
        return False

def check_license(email, password):
    """Check license validity with email, password, and expiry date"""

    print(f"{Fore.YELLOW}Checking credentials...")

    # First check credentials using configuration variables
    if email.lower() == VALID_EMAIL.lower() and password == VALID_PASSWORD:
        print(f"{Fore.GREEN}✓ Login successful!")

        # Then check license expiry
        if check_license_expiry():
            return True
        else:
            return False
    else:
        print(f"{Fore.RED}✗ Invalid email or password!")
        return False

    # PRODUCTION CODE - Uncomment this section for real license checking
    """
    try:
        print(f"{Fore.YELLOW}Checking license...")
        # You can replace this URL with your own license validation endpoint
        url = "https://pastebin.com/raw/PM1ZgpbU"  # Replace with your license URL

        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            # Check if email is in the approved list
            approved_emails = [email.strip().lower() for email in response.text.strip().split('\n') if email.strip()]
            if email.lower() in approved_emails:
                print(f"{Fore.GREEN}✓ License approved!")
                return True
            else:
                print(f"{Fore.RED}✗ License not approved. Contact @Wolvestrading1")
                return False
        else:
            print(f"{Fore.RED}✗ Unable to verify license. Contact @Wolvestrading1")
            return False
    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}✗ License check failed: Network error")
        print(f"{Fore.YELLOW}Contact @Wolvestrading1 for assistance")
        return False
    except Exception as e:
        print(f"{Fore.RED}✗ License check failed: {e}")
        return False
    """

def get_login_credentials():
    """Get email and password from user"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}                    LOGIN REQUIRED")
    print(f"{Fore.CYAN}{'='*60}")

    try:
        email = input(f"{Fore.YELLOW}Email: ").strip()

        # OPTION 1: Hidden password (secure) - uncomment this section
        """
        try:
            import getpass
            password = getpass.getpass(f"{Fore.YELLOW}Password: ")
        except:
            password = input(f"{Fore.YELLOW}Password: ").strip()
        """

        # OPTION 2: Visible password (less secure but easier) - currently active
        password = input(f"{Fore.YELLOW}Password: ").strip()

        return email, password

    except KeyboardInterrupt:
        print(f"\n{Fore.RED}Login cancelled.")
        return None, None

def get_broker_name():
    """Get broker name from user"""
    print(f"\n{Fore.CYAN}Available Brokers:")
    print(f"{Fore.WHITE}1. QUOTEX")
    print(f"{Fore.WHITE}2. POCKET OPTION")
    
    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select broker (1-2) or type name: ").strip()
            
            if choice == "1" or choice.upper() == "QUOTEX":
                return "QUOTEX"
            elif choice == "2" or choice.upper() == "POCKET OPTION":
                return "POCKET OPTION"
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1 for QUOTEX or 2 for POCKET OPTION.")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_trade_direction():
    """Get trade direction from user"""
    print(f"\n{Fore.CYAN}Trade Directions:")
    print(f"{Fore.WHITE}1. PUT (Down)")
    print(f"{Fore.WHITE}2. CALL (Up)")
    print(f"{Fore.WHITE}3. BOTH (Put & Call)")
    
    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select direction (1-3): ").strip()
            
            if choice == "1":
                return "PUT"
            elif choice == "2":
                return "CALL"
            elif choice == "3":
                return "BOTH"
            elif choice.upper() in ['PUT', 'CALL', 'BOTH']:
                return choice.upper()
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1 (PUT), 2 (CALL), or 3 (BOTH).")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def validate_currency_pair(pair):
    """Validate currency pair format (supports both real market and OTC pairs)"""
    pair = pair.strip().upper()

    # Check for OTC format
    if '-OTC' in pair:
        base_pair = pair.split('-')[0]
        if len(base_pair) == 6 and base_pair.isalpha():
            return True

    # Check for real market format (XXXYYY without -OTC)
    if len(pair) == 6 and pair.isalpha():
        return True

    return False

def get_currency_pairs():
    """Get currency pairs from user with preference for real market pairs"""
    print(f"\n{Fore.CYAN}Currency Pair Options:")
    print(f"{Fore.GREEN}RECOMMENDED (Real Market): EURUSD, GBPUSD, USDJPY, USDCHF, AUDUSD, USDCAD")
    print(f"{Fore.YELLOW}ALTERNATIVE (OTC): EURUSD-OTC, GBPUSD-OTC, etc.")
    print(f"{Fore.WHITE}Enter multiple pairs separated by commas")
    print(f"{Fore.CYAN}💡 Real market pairs typically have better accuracy than OTC pairs")

    while True:
        try:
            pairs_input = input(f"\n{Fore.YELLOW}Enter currency pairs: ").strip()

            if not pairs_input:
                print(f"{Fore.RED}Please enter at least one currency pair.")
                continue

            pairs = [pair.strip().upper() for pair in pairs_input.split(',')]

            valid_pairs = []
            invalid_pairs = []
            real_market_pairs = []
            otc_pairs = []

            for pair in pairs:
                if validate_currency_pair(pair):
                    valid_pairs.append(pair)
                    if '-OTC' in pair:
                        otc_pairs.append(pair)
                    else:
                        real_market_pairs.append(pair)
                else:
                    invalid_pairs.append(pair)

            if invalid_pairs:
                print(f"{Fore.RED}Invalid pairs: {', '.join(invalid_pairs)}")
                print(f"{Fore.YELLOW}Please use format: XXXYYY or XXXYYY-OTC")

            if valid_pairs:
                if real_market_pairs:
                    print(f"{Fore.GREEN}✅ Real Market pairs: {', '.join(real_market_pairs)}")
                if otc_pairs:
                    print(f"{Fore.YELLOW}⚠️  OTC pairs: {', '.join(otc_pairs)}")

                print(f"{Fore.CYAN}Total pairs selected: {len(valid_pairs)}")
                return valid_pairs
            else:
                print(f"{Fore.RED}No valid pairs entered. Please try again.")

        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_trading_hours():
    """Get trading start and end times from user"""
    print(f"\n{Fore.CYAN}{'='*60}")
    print(f"{Fore.CYAN}                 TRADING TIME LIMITS")
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.WHITE}Set the time range when signals should be generated")
    print(f"{Fore.YELLOW}Format: HH:MM (24-hour format, e.g., 09:30, 15:45)")

    while True:
        try:
            start_time = input(f"\n{Fore.YELLOW}START TIME (HH:MM): ").strip()

            # Validate start time format
            if not validate_time_format(start_time):
                print(f"{Fore.RED}Invalid format. Please use HH:MM (e.g., 09:30)")
                continue

            end_time = input(f"{Fore.YELLOW}END TIME (HH:MM): ").strip()

            # Validate end time format
            if not validate_time_format(end_time):
                print(f"{Fore.RED}Invalid format. Please use HH:MM (e.g., 23:59)")
                continue

            print(f"{Fore.GREEN}✓ Trading hours set: {start_time} to {end_time}")
            return start_time, end_time

        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def validate_time_format(time_str):
    """Validate time format HH:MM"""
    try:
        parts = time_str.split(':')
        if len(parts) != 2:
            return False

        hour = int(parts[0])
        minute = int(parts[1])

        if 0 <= hour <= 23 and 0 <= minute <= 59:
            return True
        return False
    except ValueError:
        return False

def select_timezone():
    """Select timezone for trading"""
    timezones = [
        ('Asia/Dhaka', 'Bangladesh      (UTC+6)'),
        ('Asia/Kolkata', 'India           (UTC+5:30)'),
        ('Asia/Karachi', 'Pakistan        (UTC+5)'),
        ('US/Eastern', 'USA (Eastern)   (UTC-5/-4)'),
        ('US/Central', 'USA (Central)   (UTC-6/-5)'),
        ('US/Mountain', 'USA (Mountain)  (UTC-7/-6)'),
        ('US/Pacific', 'USA (Pacific)   (UTC-8/-7)'),
        ('Europe/London', 'UK              (UTC+0/+1)'),
        ('Asia/Dubai', 'Dubai/UAE       (UTC+4)'),
        ('Asia/Riyadh', 'Saudi Arabia    (UTC+3)'),
        ('Asia/Kuala_Lumpur', 'Malaysia        (UTC+8)'),
        ('Asia/Kathmandu', 'Nepal           (UTC+5:45)'),
        ('Asia/Singapore', 'Singapore       (UTC+8)')
    ]

    print(f"\n{Fore.CYAN}Available Timezones:")
    for i, (tz_code, tz_name) in enumerate(timezones, 1):
        print(f"{Fore.WHITE}{i:2d}. {tz_name}")

    while True:
        try:
            choice = input(f"\n{Fore.YELLOW}Select timezone (1-{len(timezones)}): ").strip()
            choice_num = int(choice)

            if 1 <= choice_num <= len(timezones):
                selected_tz = timezones[choice_num - 1][0]
                print(f"{Fore.GREEN}Selected: {timezones[choice_num - 1][1]}")
                return selected_tz
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1-{len(timezones)}.")

        except ValueError:
            print(f"{Fore.RED}Please enter a valid number.")
        except KeyboardInterrupt:
            print(f"\n{Fore.RED}Operation cancelled.")
            sys.exit(0)

def get_formatted_time(timezone_name):
    """Get formatted time for selected timezone"""
    try:
        tz = pytz.timezone(timezone_name)
        now = datetime.datetime.now(tz)
        formatted_time = now.strftime('%d-%m-%y, Time-%H:%M')
        return formatted_time, now
    except Exception as e:
        print(f"{Fore.RED}Error getting time: {e}")
        return None, None

def show_spinner(message="Processing", duration=3):
    """Show a loading spinner with message"""
    spinner_chars = ['|', '/', '-', '\\']
    end_time = time.time() + duration
    
    while time.time() < end_time:
        for char in spinner_chars:
            if time.time() >= end_time:
                break
            print(f'\r{Fore.YELLOW}{message}... {char}', end='', flush=True)
            time.sleep(0.2)
    
    print(f'\r{Fore.GREEN}{message}... ✓{" " * 10}')

# ============================================================================
# TECHNICAL ANALYSIS AND MARKET DATA FUNCTIONS
# ============================================================================

def get_real_market_data(symbol, period="1d", interval="1m"):
    """Get real market data for analysis"""
    try:
        # Convert currency pair format (EURUSD-OTC -> EURUSD=X)
        if '-OTC' in symbol:
            base_symbol = symbol.replace('-OTC', '')
        else:
            base_symbol = symbol

        # Format for forex pairs
        if len(base_symbol) == 6 and base_symbol.isalpha():
            ticker_symbol = f"{base_symbol[:3]}{base_symbol[3:]}=X"
        else:
            ticker_symbol = base_symbol

        # Get data using yfinance
        if YFINANCE_AVAILABLE:
            ticker = yf.Ticker(ticker_symbol)
            data = ticker.history(period=period, interval=interval)

            if not data.empty:
                return data

        # Fallback to simulated data if yfinance fails
        return generate_realistic_price_data(base_symbol)

    except Exception as e:
        print(f"{Fore.YELLOW}Warning: Could not fetch real data for {symbol}, using simulated data")
        return generate_realistic_price_data(symbol)

def generate_realistic_price_data(symbol):
    """Generate realistic price data as fallback"""
    try:
        # Create realistic price movements based on typical forex ranges
        base_prices = {
            'EURUSD': 1.0800, 'GBPUSD': 1.2500, 'USDJPY': 150.00, 'USDCHF': 0.9000,
            'AUDUSD': 0.6500, 'USDCAD': 1.3500, 'NZDUSD': 0.6000, 'EURJPY': 162.00,
            'GBPJPY': 187.50, 'EURGBP': 0.8640, 'AUDCAD': 0.8775, 'AUDCHF': 0.5850
        }

        # Extract base pair from symbol
        if '-OTC' in symbol:
            base_pair = symbol.replace('-OTC', '')
        else:
            base_pair = symbol

        # Get base price or use default
        base_price = base_prices.get(base_pair, 1.2000)

        # Generate 100 realistic price points with trend and volatility
        dates = pd.date_range(end=datetime.datetime.now(), periods=100, freq='1min')

        # Create realistic price movement
        trend = np.random.choice([-1, 0, 1], p=[0.3, 0.4, 0.3])  # Bearish, sideways, bullish
        volatility = np.random.uniform(0.0001, 0.0005)  # Typical forex volatility

        prices = []
        current_price = base_price

        for i in range(100):
            # Add trend component
            trend_component = trend * 0.00001 * i

            # Add random walk
            random_change = np.random.normal(0, volatility)

            # Add some mean reversion
            mean_reversion = (base_price - current_price) * 0.01

            current_price += trend_component + random_change + mean_reversion
            prices.append(current_price)

        # Create DataFrame similar to yfinance format
        data = pd.DataFrame({
            'Open': prices,
            'High': [p * (1 + np.random.uniform(0, 0.0002)) for p in prices],
            'Low': [p * (1 - np.random.uniform(0, 0.0002)) for p in prices],
            'Close': prices,
            'Volume': [np.random.randint(1000, 10000) for _ in range(100)]
        }, index=dates)

        # Ensure High >= Close >= Low
        for i in range(len(data)):
            data.iloc[i]['High'] = max(data.iloc[i]['High'], data.iloc[i]['Close'])
            data.iloc[i]['Low'] = min(data.iloc[i]['Low'], data.iloc[i]['Close'])

        return data

    except Exception as e:
        print(f"{Fore.RED}Error generating realistic data: {e}")
        return None

def calculate_rsi(prices, period=14):
    """Calculate RSI (Relative Strength Index)"""
    try:
        if len(prices) < period + 1:
            return 50  # Neutral RSI if not enough data

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])

        if avg_loss == 0:
            return 100

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi
    except:
        return 50

def calculate_macd(prices, fast=12, slow=26, signal=9):
    """Calculate MACD (Moving Average Convergence Divergence)"""
    try:
        if len(prices) < slow:
            return 0, 0, 0  # MACD, Signal, Histogram

        # Calculate EMAs
        ema_fast = pd.Series(prices).ewm(span=fast).mean().iloc[-1]
        ema_slow = pd.Series(prices).ewm(span=slow).mean().iloc[-1]

        macd_line = ema_fast - ema_slow

        # For signal line, we need more historical MACD values
        if len(prices) >= slow + signal:
            macd_series = pd.Series(prices).ewm(span=fast).mean() - pd.Series(prices).ewm(span=slow).mean()
            signal_line = macd_series.ewm(span=signal).mean().iloc[-1]
            histogram = macd_line - signal_line
        else:
            signal_line = macd_line
            histogram = 0

        return macd_line, signal_line, histogram
    except:
        return 0, 0, 0

def calculate_bollinger_bands(prices, period=20, std_dev=2):
    """Calculate Bollinger Bands"""
    try:
        if len(prices) < period:
            current_price = prices[-1] if prices else 1.0
            return current_price, current_price, current_price  # Middle, Upper, Lower

        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])

        upper_band = sma + (std_dev * std)
        lower_band = sma - (std_dev * std)

        return sma, upper_band, lower_band
    except:
        current_price = prices[-1] if prices else 1.0
        return current_price, current_price, current_price

def calculate_moving_averages(prices, short_period=10, long_period=20):
    """Calculate short and long moving averages"""
    try:
        if len(prices) < long_period:
            return prices[-1] if prices else 1.0, prices[-1] if prices else 1.0

        short_ma = np.mean(prices[-short_period:])
        long_ma = np.mean(prices[-long_period:])

        return short_ma, long_ma
    except:
        current_price = prices[-1] if prices else 1.0
        return current_price, current_price

def calculate_volatility(prices, period=20):
    """Calculate price volatility"""
    try:
        if len(prices) < period:
            return 0.001  # Default low volatility

        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns[-period:])
        return volatility
    except:
        return 0.001

def is_high_volume_session(timezone_name):
    """Check if current time is during high-volume trading sessions"""
    try:
        tz = pytz.timezone(timezone_name)
        now = datetime.datetime.now(tz)

        # Convert to UTC for session checking
        utc_now = now.astimezone(pytz.UTC)
        hour = utc_now.hour

        # High volume sessions (UTC):
        # London: 8:00-17:00, New York: 13:00-22:00, Tokyo: 23:00-8:00
        london_session = 8 <= hour <= 17
        ny_session = 13 <= hour <= 22
        tokyo_session = hour >= 23 or hour <= 8

        # Overlap periods are highest volume
        london_ny_overlap = 13 <= hour <= 17

        if london_ny_overlap:
            return 3  # Highest volume
        elif london_session or ny_session:
            return 2  # High volume
        elif tokyo_session:
            return 1  # Medium volume
        else:
            return 0  # Low volume
    except:
        return 1  # Default medium volume

def analyze_market_conditions(data):
    """Analyze market conditions and return signal strength with enhanced filtering"""
    try:
        if data is None or data.empty:
            return {"strength": 0, "direction": "NEUTRAL", "confidence": 50}

        prices = data['Close'].values
        current_price = prices[-1]

        # Calculate volatility
        volatility = calculate_volatility(prices)

        # Check if volatility is sufficient for trading
        if volatility < 0.0001:  # Too low volatility
            return {"strength": 0, "direction": "NEUTRAL", "confidence": 50, "reason": "Low volatility"}

        # Calculate technical indicators
        rsi = calculate_rsi(prices)
        macd, macd_signal, macd_hist = calculate_macd(prices)
        bb_middle, bb_upper, bb_lower = calculate_bollinger_bands(prices)
        short_ma, long_ma = calculate_moving_averages(prices)

        # Analyze signals with enhanced criteria
        signals = []

        # RSI Analysis (more strict)
        if rsi < 25:  # More oversold
            signals.append({"type": "RSI", "direction": "CALL", "strength": 3})
        elif rsi > 75:  # More overbought
            signals.append({"type": "RSI", "direction": "PUT", "strength": 3})
        elif rsi < 35:  # Moderately oversold
            signals.append({"type": "RSI", "direction": "CALL", "strength": 2})
        elif rsi > 65:  # Moderately overbought
            signals.append({"type": "RSI", "direction": "PUT", "strength": 2})

        # MACD Analysis (enhanced)
        if macd > macd_signal and macd_hist > 0 and macd_hist > abs(macd * 0.1):
            signals.append({"type": "MACD", "direction": "CALL", "strength": 2})
        elif macd < macd_signal and macd_hist < 0 and abs(macd_hist) > abs(macd * 0.1):
            signals.append({"type": "MACD", "direction": "PUT", "strength": 2})

        # Moving Average Analysis (with trend strength)
        ma_diff = abs(short_ma - long_ma) / current_price
        if short_ma > long_ma and ma_diff > 0.0005:  # Strong uptrend
            signals.append({"type": "MA", "direction": "CALL", "strength": 2})
        elif short_ma < long_ma and ma_diff > 0.0005:  # Strong downtrend
            signals.append({"type": "MA", "direction": "PUT", "strength": 2})

        # Bollinger Bands Analysis (enhanced)
        bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
        if bb_position <= 0.1:  # Very close to lower band
            signals.append({"type": "BB", "direction": "CALL", "strength": 3})
        elif bb_position >= 0.9:  # Very close to upper band
            signals.append({"type": "BB", "direction": "PUT", "strength": 3})
        elif bb_position <= 0.2:  # Close to lower band
            signals.append({"type": "BB", "direction": "CALL", "strength": 2})
        elif bb_position >= 0.8:  # Close to upper band
            signals.append({"type": "BB", "direction": "PUT", "strength": 2})

        # Calculate overall signal
        call_strength = sum(s["strength"] for s in signals if s["direction"] == "CALL")
        put_strength = sum(s["strength"] for s in signals if s["direction"] == "PUT")

        # Require stronger confirmation (minimum 5 instead of 4)
        if call_strength > put_strength and call_strength >= 5:
            direction = "CALL"
            strength = call_strength
        elif put_strength > call_strength and put_strength >= 5:
            direction = "PUT"
            strength = put_strength
        else:
            direction = "NEUTRAL"
            strength = 0

        # Calculate confidence based on signal strength, agreement, and volatility
        max_possible_strength = 10  # Updated max strength
        base_confidence = 50 + (strength / max_possible_strength) * 40

        # Volatility bonus (higher volatility = higher confidence up to a point)
        volatility_bonus = min(10, volatility * 20000)  # Cap at 10%

        confidence = min(95, max(50, base_confidence + volatility_bonus))

        return {
            "strength": strength,
            "direction": direction,
            "confidence": round(confidence),
            "volatility": round(volatility, 6),
            "indicators": {
                "rsi": round(rsi, 2),
                "macd": round(macd, 5),
                "current_price": round(current_price, 5),
                "short_ma": round(short_ma, 5),
                "long_ma": round(long_ma, 5),
                "bb_position": round(bb_position, 3) if 'bb_position' in locals() else 0.5
            }
        }

    except Exception as e:
        print(f"{Fore.RED}Error in market analysis: {e}")
        return {"strength": 0, "direction": "NEUTRAL", "confidence": 50}

def display_trading_info(broker, direction, pairs, timezone_name, current_time, start_time, end_time):
    """Display trading configuration summary"""
    # Get license info for display
    LICENSE_EXPIRY_DATE = datetime.datetime(LICENSE_EXPIRY_YEAR, LICENSE_EXPIRY_MONTH, LICENSE_EXPIRY_DAY)
    current_date = datetime.datetime.now()
    days_remaining = (LICENSE_EXPIRY_DATE - current_date).days

    # Determine license display color
    if current_date > LICENSE_EXPIRY_DATE:
        license_color = Fore.RED
        license_text = f"EXPIRED ({abs(days_remaining)} days ago)"
    elif days_remaining <= 7:
        license_color = Fore.YELLOW
        license_text = f"EXPIRES IN {days_remaining} DAYS"
    else:
        license_color = Fore.GREEN
        license_text = f"ACTIVE ({days_remaining} days left)"

    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.CYAN}                           TRADING CONFIGURATION")
    print(f"{Fore.CYAN}{'='*80}")
    print(f"{Fore.WHITE}Broker:          {Fore.GREEN}{broker}")
    print(f"{Fore.WHITE}Direction:       {Fore.GREEN}{direction}")
    print(f"{Fore.WHITE}Currency Pairs:  {Fore.GREEN}{', '.join(pairs)}")
    print(f"{Fore.WHITE}Timezone:        {Fore.GREEN}{timezone_name}")
    print(f"{Fore.WHITE}Current Time:    {Fore.GREEN}{current_time}")
    print(f"{Fore.WHITE}Trading Hours:   {Fore.YELLOW}{start_time} to {end_time}")
    print(f"{Fore.WHITE}License Status:  {license_color}{license_text}")
    print(f"{Fore.CYAN}{'='*80}")

def generate_signal(pair, direction_filter, target_time, timezone_name):
    """Generate trading signal based on real market analysis with session filtering"""
    try:
        # Check market session volume
        session_volume = is_high_volume_session(timezone_name)
        if session_volume == 0:  # Skip during very low volume periods
            return None

        # Get real market data for analysis
        market_data = get_real_market_data(pair, period="1d", interval="1m")

        if market_data is None or market_data.empty:
            return None

        # Analyze market conditions
        analysis = analyze_market_conditions(market_data)

        # Enhanced filtering based on session and strength
        min_strength_required = 5  # Increased from 4
        if session_volume >= 2:  # High volume sessions
            min_strength_required = 4  # Can accept slightly lower strength

        # Check if analysis indicates a strong signal
        if analysis["strength"] < min_strength_required:
            return None

        # Check direction filter
        signal_direction = analysis["direction"]
        if direction_filter == "PUT" and signal_direction != "PUT":
            return None
        elif direction_filter == "CALL" and signal_direction != "CALL":
            return None
        elif signal_direction == "NEUTRAL":
            return None

        # Additional volatility check
        if "volatility" in analysis and analysis["volatility"] < 0.0001:
            return None

        # Get real current price
        current_price = analysis["indicators"]["current_price"]

        # Fixed expiry time to 1 minute only
        expiry_minutes = 1

        # Use analysis-based confidence with session bonus
        confidence = analysis["confidence"]
        if session_volume >= 2:  # High volume session bonus
            confidence = min(95, confidence + 3)

        # Get current time in the selected timezone
        tz = pytz.timezone(timezone_name)
        local_now = datetime.datetime.now(tz)
        current_timestamp = local_now.strftime('%H:%M:%S')

        return {
            'pair': pair,
            'direction': signal_direction,
            'expiry': expiry_minutes,
            'confidence': confidence,
            'entry_price': current_price,
            'timestamp': current_timestamp,
            'target_time': target_time,
            'analysis': analysis,
            'session_volume': session_volume
        }

    except Exception as e:
        print(f"{Fore.RED}Error generating signal for {pair}: {e}")
        return None

def should_generate_signal():
    """Determine if a signal should be generated based on quality filters"""
    import random

    # More selective - only generate signal 20-25% of the time for higher quality
    signal_probability = random.randint(1, 100)

    # Generate signal only if probability is within the selective range
    return signal_probability <= 20  # 20% chance of generating a signal for higher selectivity

def get_next_signal_interval():
    """Get the next signal interval in minutes for variable timing"""
    import random

    # Variable intervals: 1-3 minutes for aggressive frequency but not every candle
    intervals = [1, 2, 3]
    weights = [50, 30, 20]  # 50% chance for 1 min, 30% for 2 min, 20% for 3 min

    return random.choices(intervals, weights=weights)[0]

def display_signal(signal):
    """Display a high-quality trading signal with technical analysis"""
    direction_color = Fore.GREEN if signal['direction'] == 'CALL' else Fore.RED
    confidence_color = Fore.GREEN if signal['confidence'] >= 90 else Fore.YELLOW

    print(f"\n{Fore.CYAN}🚨 HIGH-QUALITY SIGNAL DETECTED 🚨")
    print(f"{Fore.WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"{Fore.GREEN}✨ PREMIUM SIGNAL - TECHNICAL ANALYSIS BASED")
    print(f"{Fore.WHITE}📈 Pair:       {Fore.CYAN}{signal['pair']}")
    print(f"{Fore.WHITE}📊 Direction:  {direction_color}{signal['direction']} {'📈' if signal['direction'] == 'CALL' else '📉'}")
    print(f"{Fore.WHITE}⏰ Expiry:     {Fore.YELLOW}{signal['expiry']} minute(s)")
    print(f"{Fore.WHITE}🎯 Confidence: {confidence_color}{signal['confidence']}%")
    print(f"{Fore.WHITE}💰 Entry:      {Fore.MAGENTA}{signal['entry_price']}")
    print(f"{Fore.WHITE}🕐 Signal Time: {Fore.WHITE}{signal['timestamp']}")
    print(f"{Fore.WHITE}🎯 Target Time: {Fore.YELLOW}{signal['target_time']} (Enter trade at this time)")

    # Display technical analysis if available
    if 'analysis' in signal and 'indicators' in signal['analysis']:
        indicators = signal['analysis']['indicators']
        print(f"{Fore.CYAN}📊 TECHNICAL ANALYSIS:")
        print(f"{Fore.WHITE}   RSI:        {Fore.YELLOW}{indicators.get('rsi', 'N/A')}")
        print(f"{Fore.WHITE}   MACD:       {Fore.YELLOW}{indicators.get('macd', 'N/A')}")
        print(f"{Fore.WHITE}   Short MA:   {Fore.YELLOW}{indicators.get('short_ma', 'N/A')}")
        print(f"{Fore.WHITE}   Long MA:    {Fore.YELLOW}{indicators.get('long_ma', 'N/A')}")
        print(f"{Fore.WHITE}   Volatility: {Fore.YELLOW}{signal['analysis'].get('volatility', 'N/A')}")
        print(f"{Fore.WHITE}   Strength:   {Fore.GREEN}{signal['analysis'].get('strength', 'N/A')}/10")

        # Display session information
        session_volume = signal.get('session_volume', 1)
        session_text = {0: "Low", 1: "Medium", 2: "High", 3: "Very High"}.get(session_volume, "Unknown")
        session_color = Fore.GREEN if session_volume >= 2 else Fore.YELLOW if session_volume == 1 else Fore.RED
        print(f"{Fore.WHITE}   Session:    {session_color}{session_text} Volume")

    print(f"{Fore.WHITE}⚠️  ADVANCE WARNING: {Fore.GREEN}30 seconds before target candle!")
    print(f"{Fore.WHITE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")

def is_within_trading_hours(current_time, start_time, end_time, timezone_name):
    """Check if current time is within trading hours"""
    try:
        # Get timezone-aware current time
        tz = pytz.timezone(timezone_name)
        now = datetime.datetime.now(tz)
        current_hour_min = f"{now.hour:02d}:{now.minute:02d}"

        # Handle overnight trading (e.g., 22:00 to 06:00)
        if start_time > end_time:
            # Overnight session
            return current_hour_min >= start_time or current_hour_min <= end_time
        else:
            # Same day session
            return start_time <= current_hour_min <= end_time

    except Exception as e:
        print(f"{Fore.RED}Error checking trading hours: {e}")
        return True  # Default to allow trading if error occurs

def signal_generator(pairs, direction_filter, timezone_name, start_time, end_time, stop_event):
    """Generate selective trading signals with variable intervals and quality filtering"""
    signal_count = 0
    last_status_check = 0
    last_license_check = 0
    next_signal_time = None
    signal_interval_minutes = get_next_signal_interval()

    print(f"\n{Fore.CYAN}🎯 Signal Quality Mode: SELECTIVE (High-accuracy signals only)")
    print(f"{Fore.YELLOW}📊 Initial signal interval: {signal_interval_minutes} minute(s)")
    print(f"{Fore.GREEN}✨ Filtering enabled for better accuracy")

    while not stop_event.is_set():
        # Check license expiry every 10 minutes (600 seconds)
        current_time_check = time.time()
        if current_time_check - last_license_check > 600:
            print(f"\n{Fore.CYAN}🔒 Performing periodic license check...")
            if not check_license_expiry():
                print(f"\n{Fore.RED}{'='*60}")
                print(f"{Fore.RED}    🛑 LICENSE EXPIRED - STOPPING BOT 🛑")
                print(f"{Fore.RED}{'='*60}")
                print(f"{Fore.YELLOW}Contact @Wolvestrading1 to renew your license!")
                stop_event.set()
                return
            last_license_check = current_time_check
        try:
            # Get current time in the selected timezone (NOT UTC)
            tz = pytz.timezone(timezone_name)
            now = datetime.datetime.now(tz)
            current_second = now.second
            current_minute = now.minute
            current_hour = now.hour

            # Check if we're within trading hours
            if not is_within_trading_hours(None, start_time, end_time, timezone_name):
                # Show status message every 30 seconds when outside trading hours
                if time.time() - last_status_check > 30:
                    current_time_str = f"{current_hour:02d}:{current_minute:02d}"
                    print(f"\n{Fore.YELLOW}⏰ Outside trading hours. Current time: {current_time_str}")
                    print(f"{Fore.CYAN}📅 Trading hours: {start_time} to {end_time}")
                    print(f"{Fore.WHITE}💤 Waiting for trading session to start...")
                    last_status_check = time.time()
                time.sleep(5)
                continue

            # Initialize next signal time if not set
            if next_signal_time is None:
                next_signal_time = current_minute + signal_interval_minutes

            # Check if it's time to potentially generate a signal (30 seconds before target)
            if current_second == 30:
                # Calculate target time (next minute) in the selected timezone
                target_minute = (current_minute + 1) % 60
                target_hour = current_hour
                if target_minute == 0:  # Handle hour rollover
                    target_hour = (target_hour + 1) % 24

                # Check if this is the scheduled signal time
                if target_minute == (next_signal_time % 60):
                    # Apply quality filter - only generate signal if conditions are met
                    if should_generate_signal():
                        target_time = f"{target_hour:02d}:{target_minute:02d}"

                        # Double-check if target time is still within trading hours
                        target_time_check = f"{target_hour:02d}:{target_minute:02d}"
                        if start_time > end_time:  # Overnight session
                            is_target_valid = target_time_check >= start_time or target_time_check <= end_time
                        else:  # Same day session
                            is_target_valid = start_time <= target_time_check <= end_time

                        if not is_target_valid:
                            print(f"\n{Fore.YELLOW}⏰ Target time {target_time} is outside trading hours ({start_time} to {end_time}). Skipping signal.")
                        else:
                            # Try to generate signals for all pairs and select the best one
                            best_signal = None
                            best_strength = 0

                            for pair in pairs:
                                try:
                                    signal = generate_signal(pair, direction_filter, target_time, timezone_name)
                                    if signal and 'analysis' in signal:
                                        signal_strength = signal['analysis'].get('strength', 0)
                                        if signal_strength > best_strength:
                                            best_signal = signal
                                            best_strength = signal_strength
                                except Exception as e:
                                    print(f"{Fore.YELLOW}Warning: Could not analyze {pair}: {e}")
                                    continue

                            if best_signal:
                                signal_count += 1
                                # Display signal
                                display_signal(best_signal)

                                # Show signal counter and timing info
                                print(f"{Fore.CYAN}📊 Total High-Quality Signals: {signal_count}")
                                print(f"{Fore.GREEN}🎯 Get ready to enter trade at {target_time}!")
                                print(f"{Fore.CYAN}📅 Trading hours: {start_time} to {end_time}")
                            else:
                                print(f"\n{Fore.YELLOW}🔍 No high-quality signals found in current market conditions")
                                print(f"{Fore.CYAN}✨ All pairs analyzed but no strong setups detected")
                    else:
                        print(f"\n{Fore.YELLOW}🔍 Signal opportunity detected but filtered out for quality")
                        print(f"{Fore.CYAN}✨ Waiting for higher-quality setup...")

                    # Set next signal time with variable interval
                    signal_interval_minutes = get_next_signal_interval()
                    next_signal_time = target_minute + signal_interval_minutes
                    print(f"{Fore.YELLOW}⏳ Next signal check in {signal_interval_minutes} minute(s)")

                # Wait until next minute to avoid duplicate checks
                time.sleep(60 - current_second)
            else:
                # Wait 1 second and check again
                time.sleep(1)

        except Exception as e:
            if not stop_event.is_set():
                print(f"{Fore.RED}Signal generation error: {e}")
                time.sleep(5)

def main():
    """Main application function"""
    try:
        # Clear screen
        os.system('cls' if os.name == 'nt' else 'clear')

        # Display banner
        print(generate_banner())

        # Display detailed license status
        display_license_status()

        # Get login credentials
        email, password = get_login_credentials()

        if not email or not password:
            print(f"{Fore.RED}Login cancelled!")
            return

        # Check credentials
        if not check_license(email, password):
            print(f"\n{Fore.RED}Access denied. Invalid credentials!")
            print(f"{Fore.YELLOW}Contact @Wolvestrading1 for assistance.")
            input(f"\n{Fore.YELLOW}Press Enter to exit...")
            return
        
        # Get broker
        broker = get_broker_name()
        
        # Get trade direction
        direction = get_trade_direction()
        
        # Get currency pairs
        pairs = get_currency_pairs()

        # Select timezone
        timezone_name = select_timezone()

        # Get trading hours
        start_time, end_time = get_trading_hours()

        # Get current time
        current_time, dt_obj = get_formatted_time(timezone_name)
        if not current_time:
            print(f"{Fore.RED}Failed to get current time.")
            return
        
        # Show processing
        show_spinner("Initializing WOLVE VIP System", 2)
        show_spinner("Connecting to Trading Servers", 2)
        show_spinner("Analyzing Market Trends", 3)
        
        # Display configuration
        display_trading_info(broker, direction, pairs, timezone_name, current_time, start_time, end_time)

        print(f"\n{Fore.GREEN}✓ WOLVE VIP System Ready!")
        print(f"{Fore.CYAN}🚀 Starting Selective Signal Generation...")
        print(f"{Fore.YELLOW}📊 Monitoring {len(pairs)} currency pairs")
        print(f"{Fore.WHITE}💡 High-quality signals will appear 30 seconds before target candle")
        print(f"{Fore.GREEN}🎯 Quality filtering enabled for better accuracy")
        print(f"{Fore.CYAN}⏰ Trading hours: {start_time} to {end_time}")

        # Start signal generation
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}                🔴 SELECTIVE TRADING SIGNALS 🔴")
        print(f"{Fore.CYAN}{'='*80}")
        print(f"{Fore.YELLOW}⏳ Initializing selective signal detection...")
        print(f"{Fore.GREEN}✅ High-quality signal system active!")
        print(f"{Fore.CYAN}🎯 Only premium signals will be displayed")
        print(f"\n{Fore.WHITE}Press Ctrl+C to stop signal generation...")

        # Create stop event for signal thread
        import threading
        stop_event = threading.Event()

        # Start signal generation in background thread
        signal_thread = threading.Thread(
            target=signal_generator,
            args=(pairs, direction, timezone_name, start_time, end_time, stop_event),
            daemon=True
        )
        signal_thread.start()

        try:
            # Keep main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}🛑 Stopping signal generation...")
            stop_event.set()
            print(f"{Fore.GREEN}✅ WOLVE VIP stopped successfully!")
            print(f"{Fore.CYAN}📞 Contact @Wolvestrading1 for support")
            print(f"{Fore.WHITE}Thank you for using WOLVE VIP! 🚀")
            
    except KeyboardInterrupt:
        print(f"\n{Fore.RED}Operation cancelled by user.")
    except Exception as e:
        print(f"\n{Fore.RED}An error occurred: {e}")
        print(f"{Fore.YELLOW}Please contact @Wolvestrading1 for support.")

if __name__ == "__main__":
    main()
